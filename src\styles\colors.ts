export const colors = {
  // Cores principais
  primary: '#2196F3', // Azul principal
  secondary: '#FF4081', // Rosa
  success: '#4CAF50', // Verde
  warning: '#FFC107', // <PERSON><PERSON>
  error: '#F44336', // Vermelho
  info: '#00BCD4', // Ciano

  // Tons de azul
  ocean: '#1976D2', // Azul escuro
  sea: '#64B5F6', // Azul médio
  cloud: '#FFFFFF', // Branco

  // Tons de cinza
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // Opacidades (em hexadecimal)
  opacity: {
    5: '0D',  // 5%
    10: '1A', // 10%
    20: '33', // 20%
    30: '4D', // 30%
    40: '66', // 40%
    50: '80', // 50%
    60: '99', // 60%
    70: 'B3', // 70%
    80: 'CC', // 80%
    90: 'E6', // 90%
  },

  // Gradientes
  gradients: {
    primary: 'linear-gradient(135deg, #1976D2 0%, #64B5F6 100%)',
    secondary: 'linear-gradient(135deg, #C2185B 0%, #FF4081 100%)',
    success: 'linear-gradient(135deg, #388E3C 0%, #4CAF50 100%)',
    warning: 'linear-gradient(135deg, #FFA000 0%, #FFC107 100%)',
    error: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
  },

  // Sombras
  shadows: {
    sm: '0px 2px 4px rgba(0, 0, 0, 0.05)',
    md: '0px 4px 8px rgba(0, 0, 0, 0.1)',
    lg: '0px 8px 16px rgba(0, 0, 0, 0.15)',
  },

  text: {
    primary: '#333333',
    secondary: '#666666',
    disabled: '#999999'
  }
}; 