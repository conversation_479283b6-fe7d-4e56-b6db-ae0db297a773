<div align="center">
  <img src="assets/img/LOGOSPNG/logo.png" alt="Sistema de Gerenciamento para Personal Trainers" width="220"/>

  <h1>Sistema de Gerenciamento para Personal Trainers</h1>
  
  <p>
    <img src="https://img.shields.io/badge/Progresso-60%25-blue" alt="Progresso: 60%"/>
    <img src="https://img.shields.io/badge/Versão-1.0.0-green" alt="Versão: 1.0.0"/>
    <img src="https://img.shields.io/badge/Licença-MIT-yellow" alt="Licença: MIT"/>
  </p>
  
  <p>Um aplicativo desktop completo para personal trainers gerenciarem seus clientes, avaliações físicas e treinos.</p>
</div>

<p align="center">
  <a href="#principais-funcionalidades">Funcionalidades</a> •
  <a href="#capturas-de-tela">Capturas de Tela</a> •
  <a href="#tecnologias">Tecnologias</a> •
  <a href="#requisitos">Requisitos</a> •
  <a href="#instalação">Instalação</a> •
  <a href="#estrutura">Estrutura</a> •
  <a href="#planilhas">Planilhas</a> •
  <a href="#status">Status</a> •
  <a href="#licença">Licença</a>
</p>

---

## 📋 Principais Funcionalidades

<details open>
<summary><b>Gerenciamento de Clientes</b></summary>
<br>
<ul>
  <li>✅ Cadastro completo de clientes</li>
  <li>✅ Busca e filtragem de clientes</li>
  <li>✅ Histórico de avaliações e treinos por cliente</li>
</ul>
</details>

<details open>
<summary><b>Avaliações Físicas</b></summary>
<br>
<ul>
  <li>✅ Registro de medidas antropométricas</li>
  <li>✅ Cálculo de composição corporal</li>
  <li>✅ Acompanhamento da evolução do cliente</li>
</ul>
</details>

<details open>
<summary><b>Planejamento de Treinos</b></summary>
<br>
<ul>
  <li>✅ Criação de treinos personalizados (A, B, C, D, E, F)</li>
  <li>✅ Controle de séries, repetições e cargas</li>
  <li>✅ Cálculo automático de volume de carga</li>
  <li>✅ Acompanhamento da progressão semanal</li>
</ul>
</details>

<details open>
<summary><b>Relatórios e Estatísticas</b></summary>
<br>
<ul>
  <li>✅ Visão geral dos clientes e avaliações</li>
  <li>✅ Estatísticas de evolução</li>
  <li>✅ Exportação de dados</li>
</ul>
</details>

## 📸 Capturas de Tela

<details open>
<summary><b>Página Inicial</b></summary>
<br>
<img src="src/assets/screenshots/inicio.png" alt="Página Inicial" width="800"/>
<p>Dashboard com visão geral de clientes, avaliações recentes e estatísticas.</p>
</details>

<details open>
<summary><b>Gerenciamento de Clientes</b></summary>
<br>
<img src="src/assets/screenshots/clientes.png" alt="Gerenciamento de Clientes" width="800"/>
<p>Interface para cadastro, busca e visualização de clientes.</p>
</details>

<details open>
<summary><b>Avaliações Físicas</b></summary>
<br>
<img src="src/assets/screenshots/avaliacoes-fisicas.png" alt="Avaliações Físicas" width="800"/>
<p>Registro e acompanhamento de avaliações físicas dos clientes.</p>
</details>

<details open>
<summary><b>Gráficos de Evolução</b></summary>
<br>
<img src="src/assets/screenshots/graficos-evolucoes.png" alt="Gráficos de Evolução" width="800"/>
<p>Visualização gráfica da evolução dos clientes ao longo do tempo.</p>
</details>

<details open>
<summary><b>Gerenciamento de Treinos</b></summary>
<br>
<img src="src/assets/screenshots/gerenciamento-treinos.png" alt="Gerenciamento de Treinos" width="800"/>
<p>Criação e acompanhamento de treinos personalizados.</p>
</details>


## 🚀 Tecnologias

<table>
  <tr>
    <td align="center"><b>Frontend</b></td>
    <td>React, TypeScript, Material-UI</td>
  </tr>
  <tr>
    <td align="center"><b>Backend</b></td>
    <td>Electron, SQLite (via better-sqlite3)</td>
  </tr>
  <tr>
    <td align="center"><b>Empacotamento</b></td>
    <td>Electron Builder</td>
  </tr>
</table>

## 💻 Requisitos

- Windows 10 ou superior
- macOS 10.13 ou superior
- Linux (distribuições modernas)
- 4GB de RAM (mínimo)
- 500MB de espaço em disco

## 🔧 Instalação

<details>
<summary><b>Para Desenvolvimento</b></summary>
<br>

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/personal-trainer-app.git
   cd personal-trainer-app
   ```

2. Instale as dependências:
   ```bash
   npm install
   ```

3. Execute o aplicativo em modo de desenvolvimento:
   ```bash
   npm run dev
   ```
</details>

<details>
<summary><b>Para Usuários Finais</b></summary>
<br>

1. Baixe o instalador apropriado para seu sistema operacional na seção de [Releases](https://github.com/seu-usuario/personal-trainer-app/releases).
2. Execute o instalador e siga as instruções na tela.
</details>

## 📁 Estrutura

```
personal-trainer-app/
├── public/                  # Arquivos públicos
├── src/                     # Código-fonte
│   ├── components/          # Componentes React
│   │   ├── AvaliacaoFisica/ # Componentes de avaliação física
│   │   ├── Cliente/         # Componentes de cliente
│   │   ├── Layout/          # Componentes de layout
│   │   └── Treino/          # Componentes de treino
│   ├── contexts/            # Contextos React para gerenciamento de estado
│   ├── db/                  # Configuração e inicialização do banco de dados
│   ├── models/              # Modelos de dados e funções de acesso ao banco
│   ├── pages/               # Páginas da aplicação
│   ├── types/               # Definições de tipos TypeScript
│   └── utils/               # Funções utilitárias
├── electron.js              # Ponto de entrada do Electron
└── config-overrides.js      # Configurações personalizadas do React
```

## 📊 Planilhas

O sistema inclui suporte para as seguintes planilhas:

<details>
<summary><b>Planilhas de Composição Corporal</b></summary>
<br>
<ul>
  <li>Versões específicas para homens e mulheres</li>
  <li>Cálculo de percentual de gordura corporal</li>
  <li>Análise de medidas antropométricas</li>
</ul>
</details>

<details>
<summary><b>Planilhas de Taxa Metabólica Basal e Gasto Energético Total</b></summary>
<br>
<ul>
  <li>Cálculo de TMB e GET</li>
  <li>Recomendações de ingestão calórica</li>
</ul>
</details>

<details>
<summary><b>Planilha de Volume Load</b></summary>
<br>
<ul>
  <li>Controle de treinos A, B, C, D, E, F</li>
  <li>Acompanhamento de progressão de carga</li>
  <li>Cálculo automático de volume de treino</li>
</ul>
</details>

## 📈 Status

<details>
<summary><b>Avaliação Física (100% completo)</b></summary>
<br>
<ul>
  <li>✅ Cadastro de dados básicos (peso, altura, idade)</li>
  <li>✅ Medição de dobras cutâneas</li>
  <li>✅ Cálculo de percentual de gordura (Protocolo Jackson & Pollock)</li>
  <li>✅ Medidas antropométricas</li>
  <li>✅ Histórico de avaliações</li>
  <li>✅ Gráficos de evolução</li>
  <li>✅ Comparação entre avaliações</li>
</ul>
</details>

<details>
<summary><b>Composição Corporal (100% completo)</b></summary>
<br>
<ul>
  <li>✅ Protocolo específico para homens</li>
  <li>✅ Protocolo específico para mulheres</li>
  <li>✅ Cálculo de massa magra</li>
  <li>✅ Cálculo de percentual de gordura</li>
  <li>✅ Análise de distribuição de gordura</li>
  <li>✅ Classificação do biotipo</li>
  <li>✅ Recomendações baseadas na composição</li>
</ul>
</details>

<details>
<summary><b>Análise Avançada de Composição (0% completo)</b></summary>
<br>
<ul>
  <li>⬜ Cálculo e acompanhamento do FFMI</li>
  <li>⬜ Análise de simetria corporal</li>
  <li>⬜ Gráficos comparativos temporais</li>
  <li>⬜ Indicadores de proporcionalidade</li>
  <li>⬜ Sugestões de correção de assimetrias</li>
</ul>
</details>

<details>
<summary><b>Taxa Metabólica Basal e GET (100% completo)</b></summary>
<br>
<ul>
  <li>✅ Cálculo de TMB para homens</li>
  <li>✅ Cálculo de TMB para mulheres</li>
  <li>✅ Estimativa do GET</li>
  <li>✅ Ajuste por nível de atividade</li>
  <li>✅ Recomendações calóricas</li>
  <li>✅ Metas nutricionais básicas</li>
</ul>
</details>

<details>
<summary><b>Nutrição Avançada (0% completo)</b></summary>
<br>
<ul>
  <li>⬜ Distribuição detalhada de macronutrientes</li>
  <li>⬜ Planejamento de refeições</li>
  <li>⬜ Ajuste dinâmico de calorias</li>
  <li>⬜ Recomendações de suplementação</li>
  <li>⬜ Periodização nutricional</li>
</ul>
</details>

<details>
<summary><b>Gestão de Treinos (100% completo)</b></summary>
<br>
<ul>
  <li>✅ Criação de treinos A/B/C/D/E/F</li>
  <li>✅ Registro de exercícios</li>
  <li>✅ Controle de séries e repetições</li>
  <li>✅ Registro de cargas</li>
  <li>✅ Cálculo de volume de treino</li>
  <li>✅ Progressão de cargas</li>
  <li>✅ Periodização</li>
  <li>✅ Histórico de evolução</li>
</ul>
</details>

<details>
<summary><b>Análise Avançada de Treino (0% completo)</b></summary>
<br>
<ul>
  <li>⬜ Cálculo de densidade do treino</li>
  <li>⬜ Monitoramento de tempo sob tensão</li>
  <li>⬜ Análise de fadiga e recuperação</li>
  <li>⬜ Sugestões automáticas de progressão</li>
  <li>⬜ Análise por padrão de movimento</li>
  <li>⬜ Periodização avançada de volume</li>
</ul>
</details>

<details>
<summary><b>Interface e Usabilidade (100% completo)</b></summary>
<br>
<ul>
  <li>✅ Design responsivo</li>
  <li>✅ Navegação intuitiva</li>
  <li>✅ Formulários validados</li>
  <li>✅ Feedback visual</li>
  <li>✅ Tooltips informativos</li>
</ul>
</details>

<details>
<summary><b>Atualizações Visuais (35% completo)</b></summary>
<br>

**Componentes Principais**
<ul>
  <li>✅ Layout.tsx - Sidebar e estrutura principal</li>
  <li>✅ Footer.tsx - Rodapé da aplicação</li>
  <li>✅ AvaliacoesPage.tsx - Página de avaliações físicas</li>
  <li>✅ AvaliacaoFisicaForm.tsx - Formulário de avaliação física</li>
</ul>

**Páginas Principais**
<ul>
  <li>⬜ HomePage.tsx
    <ul>
      <li>⬜ Atualizar para nova paleta de cores (preto e azul)</li>
      <li>⬜ Melhorar cards de estatísticas</li>
      <li>⬜ Aprimorar layout e espaçamento</li>
      <li>⬜ Adicionar efeitos de hover e sombras</li>
      <li>⬜ Dashboard com resumo de clientes ativos</li>
      <li>⬜ Gráficos de evolução geral</li>
      <li>⬜ Alertas e notificações importantes</li>
    </ul>
  </li>
  <li>⬜ TreinosPage.tsx
    <ul>
      <li>⬜ Atualizar para nova paleta de cores</li>
      <li>⬜ Melhorar layout das tabs e diálogos</li>
      <li>⬜ Atualizar estilos das tabelas e botões</li>
      <li>⬜ Adicionar mais feedback visual</li>
      <li>⬜ Visualização de progressão de cargas</li>
      <li>⬜ Calendário de treinos</li>
      <li>⬜ Filtros avançados de busca</li>
    </ul>
  </li>
  <li>⬜ ClientesPage.tsx
    <ul>
      <li>⬜ Atualizar para nova paleta de cores</li>
      <li>⬜ Melhorar layout dos cards e diálogos</li>
      <li>⬜ Atualizar estilos dos avatares e ícones</li>
      <li>⬜ Adicionar mais interatividade visual</li>
      <li>⬜ Status de progresso do cliente</li>
      <li>⬜ Timeline de evolução</li>
      <li>⬜ Indicadores de assiduidade</li>
    </ul>
  </li>
</ul>
</details>

## 📄 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

Para suporte, entre em contato através do email: <EMAIL>

---

<details>
<summary><h2>📑 Documentação Detalhada das Planilhas</h2></summary>

Este documento descreve detalhadamente cada uma das planilhas Excel disponíveis no sistema, explicando sua finalidade, estrutura e como utilizá-las.

### Índice

1. [Planilhas de Composição Corporal](#planilhas-de-composição-corporal-1)
   - [Composição Corporal para Homens](#composição-corporal-para-homens)
   - [Composição Corporal para Mulheres](#composição-corporal-para-mulheres)
2. [Planilhas de Taxa Metabólica Basal e Gasto Energético Total](#planilhas-de-taxa-metabólica-basal-e-gasto-energético-total-1)
   - [TMB e GET para Homens](#tmb-e-get-para-homens)
   - [TMB e GET para Mulheres](#tmb-e-get-para-mulheres)
3. [Planilha de Volume Load](#planilha-de-volume-load-1)
   - [Estrutura Geral](#estrutura-geral)
   - [Detalhamento das Abas de Treino](#detalhamento-das-abas-de-treino)

### Planilhas de Composição Corporal

As planilhas de composição corporal são ferramentas para avaliação antropométrica, permitindo o cálculo de diversos parâmetros relacionados à composição corporal do indivíduo. Existem versões específicas para homens e mulheres, considerando as diferenças fisiológicas entre os gêneros.

#### Composição Corporal para Homens

**Arquivo:** `01 - PLANILHA Composição corporal HOMEM.xlsx`

Esta planilha contém uma única aba chamada "AVALIAÇÃO" e é projetada para calcular a composição corporal masculina.

**Principais características:**
- Coleta de dados básicos: nome, peso, estatura, data de nascimento e idade
- Medição de dobras cutâneas (em milímetros): subescapular, tricipital, entre outras
- Cálculo de percentual de gordura corporal utilizando protocolos específicos para homens
- Análise da composição corporal com distribuição de massa magra e gordura
- Acompanhamento de medidas antropométricas

A planilha possui 88 linhas e 20 colunas, organizando os dados de forma estruturada para facilitar o preenchimento e a visualização dos resultados.

#### Composição Corporal para Mulheres

**Arquivo:** `01 - PLANILHA Composição corporal MULHER.xlsx`

Similar à versão masculina, esta planilha também contém uma única aba chamada "AVALIAÇÃO", mas é adaptada para os parâmetros femininos.

**Principais características:**
- Coleta de dados básicos: nome, peso, estatura, data de nascimento e idade
- Medição de dobras cutâneas (em milímetros) com protocolos específicos para mulheres
- Cálculo de percentual de gordura corporal utilizando equações adaptadas ao corpo feminino
- Análise da composição corporal com distribuição de massa magra e gordura
- Acompanhamento de medidas antropométricas

A planilha possui 88 linhas e 20 colunas, com estrutura semelhante à versão masculina, mas com adaptações para as particularidades do corpo feminino.

### Planilhas de Taxa Metabólica Basal e Gasto Energético Total

Estas planilhas são destinadas ao cálculo da Taxa Metabólica Basal (TMB) e do Gasto Energético Total (GET), fundamentais para o planejamento nutricional e de treinamento. Também existem versões específicas para homens e mulheres.

#### TMB e GET para Homens

**Arquivo:** `02 - PLANILHA TMB e GET HOMEM.xlsx`

Esta planilha contém uma única aba chamada "AVALIAÇÃO" e é projetada para calcular o gasto energético masculino.

**Principais características:**
- Coleta de dados básicos: nome, peso, estatura, idade
- Utilização da massa livre de gordura como parâmetro para cálculos mais precisos
- Cálculo da Taxa Metabólica Basal utilizando diferentes equações
- Estimativa do Gasto Energético Total considerando o nível de atividade física
- Recomendações de ingestão calórica baseadas nos objetivos (manutenção, perda ou ganho de peso)

A planilha possui 60 linhas e 15 colunas, organizando os dados de forma a facilitar a visualização dos resultados e recomendações.

#### TMB e GET para Mulheres

**Arquivo:** `02 - PLANILHA TMB e GET MULHER.xlsx`

Similar à versão masculina, esta planilha também contém uma única aba chamada "AVALIAÇÃO", mas é adaptada para os parâmetros femininos.

**Principais características:**
- Coleta de dados básicos: nome, peso, estatura, idade
- Utilização da massa livre de gordura como parâmetro para cálculos mais precisos
- Cálculo da Taxa Metabólica Basal utilizando equações específicas para mulheres
- Estimativa do Gasto Energético Total considerando o nível de atividade física
- Recomendações de ingestão calórica baseadas nos objetivos (manutenção, perda ou ganho de peso)

A planilha possui 60 linhas e 15 colunas, com estrutura semelhante à versão masculina, mas com adaptações para as particularidades metabólicas femininas.

### Planilha de Volume Load

**Arquivo:** `03 - PLANILHA Volume Load.xlsx`

Esta planilha é uma ferramenta completa para planejamento e acompanhamento de treinamento de força, utilizando o conceito de "Volume Load" (Volume de Carga), que é calculado multiplicando o número de séries pelo número de repetições pela carga utilizada em cada exercício.

#### Estrutura Geral

A planilha contém 6 abas, cada uma representando um tipo de treino diferente:
- TREINO (A)
- TREINO (B)
- TREINO (C)
- TREINO (D)
- TREINO (E)
- TREINO (F)

Cada aba possui a mesma estrutura básica, permitindo a organização de um programa de treinamento completo dividido em diferentes dias ou objetivos.

#### Detalhamento das Abas de Treino

Cada aba de treino (A a F) segue um padrão estrutural semelhante, mas pode ser personalizada para diferentes grupos musculares ou objetivos específicos:

**Cabeçalho:**
- Título principal: "VOLUME LOAD"
- Subtítulo: "TREINO X" (com espaço para especificar o foco do treino)
- Área para identificação do aluno/cliente
- Data de início do programa de treinamento

**Estrutura de Exercícios:**
- Capacidade para até 10 exercícios diferentes
- Cada exercício possui:
  - Campo para nome do exercício
  - Numeração sequencial (EXERCÍCIO 1, EXERCÍCIO 2, etc.)
  - Espaço para até 8 séries por exercício
  - Para cada série: campos para repetições (REP) e carga (CARGA)
  - Cálculo automático do volume de carga para cada série (REP × CARGA)
  - Somatório do volume total do exercício

**Organização Temporal:**
- Divisão por semanas (Semana 1 a Semana 8)
- Cada semana possui uma coluna específica para registro
- Permite visualização da progressão ao longo do tempo
- Facilita comparações entre diferentes períodos de treinamento

**Recursos Adicionais:**
- Células coloridas para facilitar a visualização
- Fórmulas automáticas para cálculos de volume
- Espaço para observações e anotações específicas
- Área para registro de sensações e percepções do treino

**Dimensões e Capacidade:**
- Cada aba possui 180 linhas e 37 colunas
- Comporta múltiplos exercícios com suas respectivas séries
- Permite o acompanhamento por várias semanas (até 8 semanas)
- Espaço suficiente para programas de treinamento completos e detalhados

**Funcionalidades Avançadas:**
- Cálculo automático do volume total por treino
- Possibilidade de comparação entre diferentes semanas
- Visualização da progressão de carga ao longo do tempo
- Adaptabilidade para diferentes metodologias de treinamento
- Compatibilidade com diversos objetivos (hipertrofia, força, resistência)

Esta planilha é ideal para profissionais de Educação Física e personal trainers que desejam estruturar programas de treinamento de forma organizada e científica, permitindo o acompanhamento da progressão de carga e volume ao longo do tempo, fatores fundamentais para a hipertrofia muscular e ganhos de força.
</details>

<div align="center">
  <p>Desenvolvido com ❤️ para profissionais de Educação Física</p>
</div>
